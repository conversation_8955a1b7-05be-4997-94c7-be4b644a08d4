DJANGO_ENV=development

# ----------------------------------
# Docker Settings
# ----------------------------------
GID=1001
UID=1001
DOCKER_IMAGE_TAG=local
FORWARD_API_PORT=80
FORWARD_DJANGO_PORT=8000
FORWARD_DJANGO_SOCKET_PORT=8001
FORWARD_PGADMIN_PORT=5050
FORWARD_MAILPIT_PORT=8025

ENABLE_SETTING_APP=True
ENABLE_USER_APP=True
ENABLE_CMS_APP=True

# ----------------------------------
# Django Settings
# ----------------------------------
DJANGO_SECRET_KEY="akr2icmg1n8%z^3fe3c+)5d0(t^cy-2_25rrl35a7@!scna^1#"
DJANGO_ALLOWED_HOSTS="admin.devxhub.com,devxhub.com,localhost,127.0.0.1,0.0.0.0"
DJANGO_TRUSTED_CORS_ORIGINS="https://devxhub.com,https://admin.devxhub.com,http://localhost:8000,http://localhost:3000"
DJANGO_CSRF_TRUSTED_ORIGINS="https://devxhub.com,https://admin.devxhub.com,http://localhost:8000,http://localhost:3000"
DJANGO_CORS_ALLOW_ALL_ORIGINS=false
DJANGO_ADMIN_URL="admin/"
DJANGO_DEBUG=true
DJANGO_DEFAULT_FILE_STORAGE=local
DJANGO_DEFAULT_EMAIL_SERVICE=smtp

# Django Admin Settings
DJANGO_SUPERUSER_USERNAME=devxhub
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=1Password!

#Customer Support Settings
CUSTOMER_SUPPORT_EMAIL=<EMAIL>

# ----------------------------------
# Database Settings
# ----------------------------------
# DB_ENGINE=django.db.backends.postgresql
# DB_NAME=devxhub
# DB_USER=devxhub
# DB_PASSWORD=password
# DB_HOST=postgres
# DB_PORT=5432

# DB_ENGINE=django.db.backends.sqlite3
# DB_NAME=devxhub
# DB_USER=devxhub
# DB_PASSWORD=password
# DB_HOST=postgres
# DB_PORT=5432

DB_NAME=devxhub
DB_USER=devxhub
DB_PASSWORD=password
DB_HOST=postgres
DB_PORT=5432
DATABASE_URL="*****************************************/devxhub"
# DATABASE_URL="sqlite:///db.sqlite3" # For local development

# ----------------------------------
# AWS Settings
# ----------------------------------
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=

# AWS S3 Settings
AWS_STORAGE_BUCKET_NAME=
AWS_S3_REGION_NAME=
AWS_S3_CUSTOM_DOMAIN=

# === Stripe === #
ENABLE_STRIPE=True
STRIPE_PUBLIC_KEY=
STRIPE_SECRET_KEY= 
STRIPE_WEBHOOK_SECRET= 

# ----------------------------------
# Sentry Settings
# ----------------------------------
ENABLE_SENTRY=False 
SENTRY_ENV=development
SENTRY_DSN=

# ----------------------------------
# Email Settings for Development
# ----------------------------------
DJANGO_EMAIL_HOST=mailpit
DJANGO_EMAIL_HOST_USER=
DJANGO_EMAIL_HOST_PASSWORD=
DJANGO_EMAIL_PORT=1025
DJANGO_DEFAULT_FROM_EMAIL="devxhub <<EMAIL>>"
DJANGO_DEFAULT_TO_EMAIL=
DJANGO_EMAIL_USE_TLS=False  
# ----------------------------------
# Email Settings for Production with SendGrid
# ----------------------------------
SENDGRID_API_KEY=""
SENDGRID_GENERATE_MESSAGE_ID=""
SENDGRID_MERGE_FIELD_FORMAT=""
SENDGRID_API_URL="https://api.sendgrid.com/v3/"

# ----------------------------------
# Email Settings for Production with AWS SES
# ----------------------------------
AWS_SES_ACCESS_KEY_ID=""
AWS_SES_SECRET_ACCESS_KEY=""
AWS_SES_REGION_NAME="us-east-1"

# ----------------------------------
# Email Settings for Production with Mailgun
# ----------------------------------
MAILGUN_API_KEY=""
MAILGUN_SENDER_DOMAIN="devxhub.com"
MAILGUN_API_URL="https://api.mailgun.net/v3"

# ----------------------------------
# Social Auth Settings
# ----------------------------------
SOCIAL_AUTH_GOOGLE_OAUTH2_KEY=
SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET=
SOCIAL_AUTH_FACEBOOK_KEY=
SOCIAL_AUTH_FACEBOOK_SECRET=


# ----------------------------------
# Account Settings
# ----------------------------------
ACCOUNT_AUTHENTICATION_METHOD="email"
ACCOUNT_EMAIL_VERIFICATION="mandatory"

# ----------------------------------
# Valkey and Celery Settings
# ----------------------------------
VALKEY_HOST=valkey
VALKEY_PORT=6379
VALKEY_URL=valkey://valkey:6379/0

CELERY_BROKER_URL=redis://valkey:6379/0
CELERY_RESULT_BACKEND=redis://valkey:6379/0

# ----------------------------------
# reCAPTCHA Settings
# ----------------------------------
RECAPTCHA_BASE_URL=https://recaptchaenterprise.googleapis.com/v1/projects
RECAPTCHA_PROJECT_ID="devxhub"
RECAPTCHA_SITE_KEY= 
GOOGLE_RECAPTCHA_API_KEY=
RECAPTCHA_MIN_SCORE=0.5
RECAPTCHA_ENABLE=False

# ----------------------------------
# Docker Settings for Development 
# ----------------------------------
USE_DOCKER=True

# ----------------------------------
# OpenAI Settings
# ----------------------------------
ENABLE_OPENAI=False
OPENAI_API_KEY=openai_api_key
OPENAI_MAX_TOKENS=1500

# ----------------------------------
# API Rate Limit Settings 
# Example: 100/minute
# Example: 100/hour
# ----------------------------------

ANONIMUS_API_RATE_LIMIT=100/hour
AUTHENTICATED_API_RATE_LIMIT=1000/minute

GOOGLE_REDIRECT_URL=http://localhost:3000/en/oauth/google/callback
FACEBOOK_REDIRECT_URL=http://localhost:3000/en/oauth/facebook/callback
LINKEDIN_REDIRECT_URL=http://localhost:3000/en/oauth/linkedin/callback

LICENSE_KEY=#959554848


#  ----------------------------------
#  Admin panel settings
#  ----------------------------------
ENABLE_UNFOLD=True

#  ----------------------------------
#  Swagger settings
#  ----------------------------------
ENABLE_SWAGGER=False

#  ----------------------------------
#  Debug toolbar settings
#  ----------------------------------
ENABLE_DEBUG_TOOLBAR=True

# ----------------------------------
# MFA Settings
# ----------------------------------
APP_NAME="DevXHub"

# ----------------------------------
# AWS S3 Settings
# ----------------------------------
USE_AWS_S3=True

# ----------------------------------
# Deepseek Settings
# ----------------------------------
DEEPSEEK_API_KEY=deepseek_api_key

# ----------------------------------
# Media Base Url
# ----------------------------------
MEDIA_BASE_URL="http://localhost:8000"

# ----------------------------------
# Django Encrypted Field key
# ----------------------------------
FIELD_ENCRYPTION_KEY=tzhVN_tY_4Fr6KfUgoZNWaCTnh1_BEFZBC8HRD95Gxk=

# ----------------------------------
# Twilio Settings
# ----------------------------------
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=