from dxh_common.base.base_serializer import BaseSerializer
from dxh_libraries.rest_framework import serializers
from dxh_common.utils.enums import MFAType


class MFAActivationSerializer(BaseSerializer):
    mfa_type = serializers.ChoiceField(choices=MFAType.choices())
    phone_number = serializers.CharField(required=False, allow_blank=False)

    def validate(self, attrs):
        mfa_type = attrs.get("mfa_type")
        phone_number = attrs.get("phone_number")
        if mfa_type == MFAType.SMS.value and not phone_number:
            raise serializers.ValidationError({
                "phone_number": "Phone number is required for SMS MFA."
            })

        return attrs


class MFAVerifySMSSerializer(BaseSerializer):
    phone_number = serializers.CharField(required=True, allow_blank=False)
    code = serializers.CharField(required=True, allow_blank=False)
